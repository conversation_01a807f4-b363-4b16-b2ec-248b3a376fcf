@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 208 25% 20%;

    --card: 0 0% 100%;
    --card-foreground: 208 25% 20%;

    --popover: 0 0% 100%;
    --popover-foreground: 208 25% 20%;

    --primary: 195 85% 41%;
    --primary-foreground: 0 0% 100%;

    --secondary: 195 15% 95%;
    --secondary-foreground: 208 25% 20%;

    --muted: 195 15% 96%;
    --muted-foreground: 208 15% 50%;

    --accent: 195 55% 85%;
    --accent-foreground: 208 25% 20%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 195 15% 90%;
    --input: 195 15% 94%;
    --ring: 195 85% 41%;

    --radius: 0.75rem;

    /* Hala Brand Colors */
    --brand-teal: 195 85% 41%;
    --brand-teal-light: 195 55% 85%;
    --brand-teal-dark: 195 85% 35%;
    --brand-ocean: 200 40% 60%;
    --brand-warm: 45 30% 92%;

    /* Gradients */
    --gradient-ocean: linear-gradient(
      135deg,
      hsl(var(--brand-teal)) 0%,
      hsl(var(--brand-ocean)) 100%
    );
    --gradient-warm: linear-gradient(
      135deg,
      hsl(var(--brand-warm)) 0%,
      hsl(var(--background)) 100%
    );

    /* Shadows */
    --shadow-soft: 0 4px 20px -4px hsl(var(--brand-teal) / 0.15);
    --shadow-elevated: 0 8px 30px -8px hsl(var(--brand-teal) / 0.25);

    /* Animation */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 208 30% 8%;
    --foreground: 0 0% 98%;

    --card: 208 25% 12%;
    --card-foreground: 0 0% 98%;

    --popover: 208 25% 12%;
    --popover-foreground: 0 0% 98%;

    --primary: 195 85% 65%;
    --primary-foreground: 208 25% 15%;

    --secondary: 208 15% 18%;
    --secondary-foreground: 0 0% 90%;

    --muted: 208 15% 15%;
    --muted-foreground: 208 10% 60%;

    --accent: 195 35% 25%;
    --accent-foreground: 0 0% 90%;

    --destructive: 0 62.8% 50%;
    --destructive-foreground: 0 0% 98%;

    --border: 208 15% 20%;
    --input: 208 15% 18%;
    --ring: 195 85% 65%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Custom animations */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-50px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(50px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes float {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-20px);
    }
  }

  @keyframes pulse-glow {
    0%,
    100% {
      box-shadow: 0 0 20px rgba(var(--brand-teal-rgb), 0.3);
    }
    50% {
      box-shadow: 0 0 40px rgba(var(--brand-teal-rgb), 0.6);
    }
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
  }

  .animate-fade-in {
    animation: fadeIn 0.8s ease-out forwards;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out forwards;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.6s ease-out forwards;
  }

  .animate-scale-in {
    animation: scaleIn 0.5s ease-out forwards;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  /* New enhanced animations for features section */
  @keyframes spin-slow {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  @keyframes gradient-shift {
    0%,
    100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  .animate-spin-slow {
    animation: spin-slow 3s linear infinite;
  }

  .animate-shimmer {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
  }

  /* Hover effects */
  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-elevated);
  }

  /* Enhanced card effects */
  .card-perspective {
    perspective: 1000px;
  }

  .card-3d {
    transform-style: preserve-3d;
    transition: transform 0.6s cubic-bezier(0.23, 1, 0.32, 1);
  }

  .card-3d:hover {
    transform: rotateY(5deg) rotateX(5deg);
  }

  /* RTL Support */
  [dir="rtl"] {
    direction: rtl;
  }

  [dir="rtl"] .text-left {
    text-align: right;
  }

  [dir="rtl"] .text-right {
    text-align: left;
  }

  [dir="rtl"] .justify-start {
    justify-content: flex-end;
  }

  [dir="rtl"] .justify-end {
    justify-content: flex-start;
  }

  /* Arabic font optimization */
  [dir="rtl"] {
    font-family: "Inter", "Arial", "Helvetica Neue", sans-serif;
  }

  /* Adjust spacing for Arabic text */
  [dir="rtl"] .tracking-tight {
    letter-spacing: 0;
  }

  [dir="rtl"] .tracking-wide {
    letter-spacing: 0.025em;
  }
}
