import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Star, Quote, Heart, MapPin } from "lucide-react";

const TestimonialsSection = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      location: "New York, NY",
      avatar: "SC",
      rating: 5,
      textKey: "testimonials.sarah.text",
      verified: true,
      stayDuration: "3 nights",
      property: "Dubai Marina Penthouse",
    },
    {
      id: 2,
      name: "Marcus Johnson",
      location: "London, UK",
      avatar: "MJ",
      rating: 5,
      textKey: "testimonials.marcus.text",
      verified: true,
      stayDuration: "1 week",
      property: "NYC Central Park View",
    },
    {
      id: 3,
      name: "<PERSON>",
      location: "Barcelona, Spain",
      avatar: "ER",
      rating: 5,
      textKey: "testimonials.elena.text",
      verified: true,
      stayDuration: "5 nights",
      property: "London Thames Apartment",
    },
  ];

  return (
    <section className="py-24 bg-gradient-warm relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute top-20 right-20 opacity-10">
        <Quote className="w-32 h-32 text-primary" />
      </div>
      <div className="absolute bottom-20 left-20 opacity-10">
        <Heart className="w-24 h-24 text-primary" />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-20">
          <div className="inline-flex items-center bg-primary/10 rounded-full px-4 py-2 mb-6">
            <Heart className="w-4 h-4 text-primary mr-2" />
            <span className="text-primary text-sm font-semibold">Guest Stories</span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6 leading-tight">
            {t('testimonials.title')}
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            {t('testimonials.description')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card
              key={testimonial.id}
              className="group border-0 shadow-soft hover:shadow-elevated transition-all duration-500 cursor-pointer hover:-translate-y-2 bg-gradient-to-br from-background to-brand-warm/30"
              style={{ animationDelay: `${index * 150}ms` }}
            >
              <CardContent className="p-8 relative overflow-hidden">
                {/* Quote decoration */}
                <div className="absolute top-4 right-4 opacity-10">
                  <Quote className="w-8 h-8 text-primary" />
                </div>

                {/* Rating Stars */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-1">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star
                        key={i}
                        className="h-5 w-5 fill-yellow-400 text-yellow-400"
                      />
                    ))}
                  </div>
                  {testimonial.verified && (
                    <Badge className="bg-green-100 text-green-700 hover:bg-green-200">
                      ✓ Verified
                    </Badge>
                  )}
                </div>

                {/* Testimonial Text */}
                <blockquote className="text-foreground leading-relaxed mb-6 relative">
                  <span className="text-primary text-4xl font-serif absolute -top-2 -left-1 opacity-20">"</span>
                  <span className="relative z-10">{t(testimonial.textKey)}</span>
                </blockquote>

                {/* Property info */}
                <div className="mb-6 p-3 bg-primary/5 rounded-lg">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-2 text-muted-foreground">
                      <MapPin className="w-4 h-4" />
                      <span>{testimonial.property}</span>
                    </div>
                    <span className="text-primary font-medium">{testimonial.stayDuration}</span>
                  </div>
                </div>

                {/* Author */}
                <div className={`flex items-center ${isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'}`}>
                  <Avatar className="h-14 w-14 ring-2 ring-primary/20">
                    <AvatarImage src="" alt={testimonial.name} />
                    <AvatarFallback className="bg-gradient-ocean text-white font-bold text-lg">
                      {testimonial.avatar}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-bold text-foreground group-hover:text-primary transition-colors duration-300">
                      {testimonial.name}
                    </p>
                    <p className="text-sm text-muted-foreground flex items-center">
                      <MapPin className="w-3 h-3 mr-1" />
                      {testimonial.location}
                    </p>
                  </div>
                </div>

                {/* Decorative line */}
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-primary/30 to-transparent transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="inline-flex items-center bg-background/50 backdrop-blur-sm rounded-2xl p-6 shadow-soft">
            <div className="flex items-center space-x-4">
              <div className="flex -space-x-2">
                <Avatar className="w-10 h-10 ring-2 ring-background">
                  <AvatarFallback className="bg-gradient-ocean text-white text-sm font-semibold">
                    SC
                  </AvatarFallback>
                </Avatar>
                <Avatar className="w-10 h-10 ring-2 ring-background">
                  <AvatarFallback className="bg-gradient-to-r from-pink-500 to-pink-600 text-white text-sm font-semibold">
                    MJ
                  </AvatarFallback>
                </Avatar>
                <Avatar className="w-10 h-10 ring-2 ring-background">
                  <AvatarFallback className="bg-gradient-to-r from-purple-500 to-purple-600 text-white text-sm font-semibold">
                    ER
                  </AvatarFallback>
                </Avatar>
              </div>
              <div className="text-left">
                <p className="font-semibold text-foreground">Join 50,000+ happy guests</p>
                <p className="text-sm text-muted-foreground">Experience luxury accommodations worldwide</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
