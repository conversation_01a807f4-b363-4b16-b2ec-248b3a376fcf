import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import { useNavigate } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Home,
  Shield,
  Wrench,
  Car,
  UtensilsCrossed,
  Plane,
  ArrowRight,
  Star,
  DollarSign,
  Tag,
  Clock,
  Users,
} from "lucide-react";

// Import images
import image1 from "@/assets/2e9a779a-39b0-402b-95e2-2ed025328301.jpg";
import image2 from "@/assets/7a373d21-f97a-49c7-a2f8-089bca45a13f.jpg";
import image3 from "@/assets/92106de0-dd6d-4b21-aff8-b438e2a6216a.jpg";
import image4 from "@/assets/647b6c1f-d7d3-4cb9-a3c3-dc433d23b357.jpg";
import image5 from "@/assets/92fcb9aa-effa-4243-a82d-4cd663f0b013.jpg";
import image6 from "@/assets/729cf90f-d9f7-47a9-b724-166c19eda1bc.jpg";
import image7 from "@/assets/302ded49-010c-4cfb-820a-e80248f358e8.jpg";
import image8 from "@/assets/9d8726ef-7525-46ab-9aa0-96b450e01622.jpg";

const FeaturedSections = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();

  // Sample units data
  const units = [
    {
      id: 1,
      title: "شقة فاخرة في المركز",
      image: image1,
      price: "2,500 ر.س/شهر",
      rating: 4.8,
      bedrooms: 2,
      bathrooms: 2,
      area: "120 م²",
    },
    {
      id: 2,
      title: "استوديو حديث",
      image: image2,
      price: "1,800 ر.س/شهر",
      rating: 4.6,
      bedrooms: 1,
      bathrooms: 1,
      area: "65 م²",
    },
    {
      id: 3,
      title: "بنتهاوس مع إطلالة",
      image: image3,
      price: "4,200 ر.س/شهر",
      rating: 4.9,
      bedrooms: 3,
      bathrooms: 3,
      area: "180 م²",
    },
    {
      id: 4,
      title: "فيلا عصرية",
      image: image4,
      price: "5,800 ر.س/شهر",
      rating: 4.9,
      bedrooms: 4,
      bathrooms: 3,
      area: "250 م²",
    },
    {
      id: 5,
      title: "شقة عائلية واسعة",
      image: image5,
      price: "3,200 ر.س/شهر",
      rating: 4.7,
      bedrooms: 3,
      bathrooms: 2,
      area: "140 م²",
    },
    {
      id: 6,
      title: "لوفت مع تراس",
      image: image6,
      price: "2,900 ر.س/شهر",
      rating: 4.8,
      bedrooms: 2,
      bathrooms: 2,
      area: "110 م²",
    },
  ];

  // Services data
  const services = [
    {
      id: 1,
      name: "خدمة النظافة",
      icon: Shield,
      description: "تنظيف شامل للوحدة",
      price: "150 ر.س",
    },
    {
      id: 2,
      name: "الصيانة",
      icon: Wrench,
      description: "صيانة فورية 24/7",
      price: "من 100 ر.س",
    },
    {
      id: 3,
      name: "خدمة التوصيل",
      icon: Car,
      description: "توصيل من وإلى المطار",
      price: "80 ر.س",
    },
    {
      id: 4,
      name: "طلب الوجبات",
      icon: UtensilsCrossed,
      description: "وجبات محضرة طازجة",
      price: "50 ر.س",
    },
    {
      id: 5,
      name: "حجز التاكسي",
      icon: Plane,
      description: "مواصلات سريعة ومريحة",
      price: "25 ر.س",
    },
  ];

  // Special offers data
  const offers = [
    {
      id: 1,
      title: "عرض الإقامة الطويلة",
      description: "خصم 20% على الإقامة أكثر من 30 يوم",
      discount: "20%",
      validUntil: "31 ديسمبر 2024",
      type: "limited",
    },
    {
      id: 2,
      title: "باقة الخدمات الشاملة",
      description: "نظافة + صيانة + وجبات بسعر مخفض",
      discount: "300 ر.س",
      originalPrice: "500 ر.س",
      type: "bundle",
    },
    {
      id: 3,
      title: "عرض العملاء الجدد",
      description: "أول إقامة بخصم 15%",
      discount: "15%",
      validUntil: "نهاية الشهر",
      type: "new-customer",
    },
  ];

  return (
    <div className="py-16 space-y-24">
      {/* Units Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <div className="inline-flex items-center bg-primary/10 rounded-full px-4 py-2 mb-6">
            <Home className="w-4 h-4 text-primary mr-2" />
            <span className="text-primary text-sm font-semibold">
              الوحدات المتاحة
            </span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6 leading-tight">
            وحدات مميزة للإيجار
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            اختر من مجموعة متنوعة من الوحدات السكنية المجهزة بالكامل
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {units.map((unit, index) => (
            <Card
              key={unit.id}
              className="group overflow-hidden border-0 shadow-soft hover:shadow-elevated transition-all duration-500 cursor-pointer transform hover:-translate-y-2"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="relative overflow-hidden">
                <img
                  src={unit.image}
                  alt={unit.title}
                  className="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />

                {/* Rating */}
                <div className="absolute top-4 right-4 bg-white/20 backdrop-blur-sm rounded-lg px-2 py-1">
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    <span className="text-white text-sm font-semibold">
                      {unit.rating}
                    </span>
                  </div>
                </div>

                {/* Price */}
                <div className="absolute top-4 left-4">
                  <Badge className="bg-primary text-white font-bold px-3 py-2">
                    {unit.price}
                  </Badge>
                </div>

                {/* Unit details overlay */}
                <div className="absolute bottom-4 left-4 right-4 text-white">
                  <h3 className="text-xl font-bold mb-2">{unit.title}</h3>
                  <div className="flex items-center space-x-4 text-sm">
                    <span>{unit.bedrooms} غرف نوم</span>
                    <span>{unit.bathrooms} حمام</span>
                    <span>{unit.area}</span>
                  </div>
                </div>
              </div>

              <CardContent className="p-6">
                <Button
                  variant="ghost"
                  onClick={() => navigate(`/property/${unit.id}`)}
                  className="w-full group-hover:bg-primary group-hover:text-white transition-all duration-300 justify-between font-semibold text-primary border border-primary/20 hover:border-primary"
                >
                  <span>عرض التفاصيل</span>
                  <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Special Offers Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <div className="inline-flex items-center bg-orange-100 rounded-full px-4 py-2 mb-6">
            <Tag className="w-4 h-4 text-orange-600 mr-2" />
            <span className="text-orange-600 text-sm font-semibold">
              العروض الخاصة
            </span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6 leading-tight">
            عروض وخصومات حصرية
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            اغتنم الفرصة واحجز الآن بأفضل الأسعار
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {offers.map((offer, index) => (
            <Card
              key={offer.id}
              className={`group overflow-hidden border-2 transition-all duration-500 cursor-pointer transform hover:-translate-y-2 ${
                offer.type === "limited"
                  ? "border-red-200 bg-red-50"
                  : offer.type === "bundle"
                  ? "border-green-200 bg-green-50"
                  : "border-blue-200 bg-blue-50"
              }`}
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <CardContent className="p-6">
                {/* Discount Badge */}
                <div className="flex justify-between items-start mb-4">
                  <Badge
                    className={`text-white font-bold px-3 py-2 text-lg ${
                      offer.type === "limited"
                        ? "bg-red-500"
                        : offer.type === "bundle"
                        ? "bg-green-500"
                        : "bg-blue-500"
                    }`}
                  >
                    خصم {offer.discount}
                  </Badge>
                  {offer.type === "limited" && (
                    <div className="flex items-center text-red-600 text-xs">
                      <Clock className="w-3 h-3 mr-1" />
                      <span>عرض محدود</span>
                    </div>
                  )}
                </div>

                <h3 className="font-bold text-xl mb-3">{offer.title}</h3>
                <p className="text-muted-foreground mb-4">
                  {offer.description}
                </p>

                {offer.originalPrice && (
                  <div className="flex items-center space-x-2 mb-4">
                    <span className="line-through text-muted-foreground">
                      {offer.originalPrice}
                    </span>
                    <DollarSign className="w-4 h-4 text-green-600" />
                  </div>
                )}

                {offer.validUntil && (
                  <div className="flex items-center text-sm text-muted-foreground mb-4">
                    <Clock className="w-4 h-4 mr-2" />
                    <span>ساري حتى {offer.validUntil}</span>
                  </div>
                )}

                <Button
                  className={`w-full group-hover:shadow-lg transition-all duration-300 ${
                    offer.type === "limited"
                      ? "bg-red-500 hover:bg-red-600"
                      : offer.type === "bundle"
                      ? "bg-green-500 hover:bg-green-600"
                      : "bg-blue-500 hover:bg-blue-600"
                  }`}
                >
                  احجز الآن
                  <ArrowRight className="w-4 h-4 ml-2 transition-transform group-hover:translate-x-1" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>
    </div>
  );
};

export default FeaturedSections;
