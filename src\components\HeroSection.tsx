import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import SearchForm from "./SearchForm";
import heroImage from "@/assets/9d8726ef-7525-46ab-9aa0-96b450e01622.jpg";
import { ChevronDown, Star, Award, Users } from "lucide-react";

const HeroSection = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image with Parallax Effect */}
      <div className="absolute inset-0 z-0">
        <img
          src={heroImage}
          alt="Modern luxury apartment interior with comfortable living space"
          className="w-full h-full object-cover transform scale-110 transition-transform duration-75"
        />
        <div className="absolute inset-0 bg-gradient-to-br from-black/30 via-black/20 to-primary/20" />
      </div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 z-5 animate-bounce delay-100">
        <div className="w-16 h-16 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center">
          <Star className="w-8 h-8 text-white" />
        </div>
      </div>
      <div className="absolute top-32 right-16 z-5 animate-bounce delay-300">
        <div className="w-12 h-12 bg-primary/20 backdrop-blur-sm rounded-full flex items-center justify-center">
          <Award className="w-6 h-6 text-white" />
        </div>
      </div>
      <div className="absolute bottom-40 left-20 z-5 animate-bounce delay-500">
        <div className="w-14 h-14 bg-white/15 backdrop-blur-sm rounded-full flex items-center justify-center">
          <Users className="w-7 h-7 text-white" />
        </div>
      </div>

      {/* Content */}
      <div className="relative z-10 w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12 animate-fade-in-up">
          <div className="inline-flex items-center bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 mb-6">
            <span className="text-white/90 text-sm font-medium">
              ✨ Experience Luxury Living
            </span>
          </div>
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 tracking-tight leading-tight">
            {t('hero.title')}
            <br />
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-white to-brand-teal-light">
              {t('hero.subtitle')}
            </span>
          </h1>
          <p className="text-lg md:text-xl text-white/90 max-w-2xl mx-auto leading-relaxed mb-8">
            {t('hero.description')}
          </p>
          
          {/* Trust Indicators */}
          <div className="flex items-center justify-center space-x-8 mb-8 text-white/80">
            <div className="flex items-center space-x-2">
              <Star className="w-5 h-5 fill-yellow-400 text-yellow-400" />
              <span className="text-sm font-medium">4.9 Rating</span>
            </div>
            <div className="flex items-center space-x-2">
              <Users className="w-5 h-5" />
              <span className="text-sm font-medium">50K+ Happy Guests</span>
            </div>
            <div className="flex items-center space-x-2">
              <Award className="w-5 h-5" />
              <span className="text-sm font-medium">Award Winning</span>
            </div>
          </div>
        </div>

        {/* Search Form */}
        <div className="max-w-4xl mx-auto animate-fade-in-up">
          <SearchForm />
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10 animate-bounce">
        <div className="flex flex-col items-center text-white/70">
          <span className="text-sm mb-2">Discover More</span>
          <ChevronDown className="w-6 h-6" />
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-background to-transparent z-5" />
    </section>
  );
};

export default HeroSection;