import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { 
  ChevronDown, 
  Globe, 
  Menu, 
  Home, 
  Users, 
  BookOpen, 
  Briefcase, 
  Newspaper,
  Building2,
  MessageCircle,
  Building,
  LogIn,
  UserPlus
} from "lucide-react";
import logoImage from "@/assets/logo.png";
import { Link } from "react-router-dom";

const Header = () => {
  const { t } = useTranslation();
  const { language, changeLanguage, isRTL } = useLanguage();

  return (
    <header className="w-full bg-background/95 backdrop-blur-md border-b border-border/50 sticky top-0 z-50 shadow-soft">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-18">
          {/* Logo */}
          <div className="flex items-center">
            <Link to="/" className="flex items-center group cursor-pointer">
              <img
                src={logoImage}
                alt="Hala Logo"
                className="w-16 h-16 object-contain transition-transform duration-300 group-hover:scale-110"
              />
            </Link>
          </div>

          {/* Navigation - Desktop */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link to="/">
              <Button
                variant="ghost"
                className="text-foreground hover:text-primary"
              >
                <Home className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                {t("nav.home")}
              </Button>
            </Link>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="text-foreground hover:text-primary"
                >
                  <Globe className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                  {language === "ar" ? "العربية" : "English"}
                  <ChevronDown
                    className={`h-4 w-4 ${isRTL ? "mr-1" : "ml-1"}`}
                  />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-background border border-border shadow-elevated z-50">
                <DropdownMenuItem onClick={() => changeLanguage("en")}>
                  English
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => changeLanguage("ar")}>
                  العربية
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="text-foreground hover:text-primary"
                >
                  <Users className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                  {t("nav.about")}
                  <ChevronDown
                    className={`h-4 w-4 ${isRTL ? "mr-1" : "ml-1"}`}
                  />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-background border border-border shadow-elevated z-50">
                <DropdownMenuItem className="flex items-center">
                  <BookOpen className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                  {t("nav.ourStory")}
                </DropdownMenuItem>
                <DropdownMenuItem className="flex items-center">
                  <Building2 className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                  {t("nav.howItWorks")}
                </DropdownMenuItem>
                <DropdownMenuItem className="flex items-center">
                  <Briefcase className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                  {t("nav.careers")}
                </DropdownMenuItem>
                <DropdownMenuItem className="flex items-center">
                  <Newspaper className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                  {t("nav.press")}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Button
              variant="ghost"
              className="text-foreground hover:text-primary"
            >
              <Building className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
              {t("nav.corporateStay")}
            </Button>
            <Button
              variant="ghost"
              className="text-foreground hover:text-primary"
            >
              <MessageCircle className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
              {t("nav.contact")}
            </Button>
            <Button
              variant="ghost"
              className="text-foreground hover:text-primary"
            >
              <Building2 className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
              {t("nav.realEstate")}
            </Button>
            <Button
              variant="ghost"
              className="text-foreground hover:text-primary"
            >
              <LogIn className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
              {t("nav.login")}
            </Button>
            <Button className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-soft">
              <UserPlus className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
              {t("nav.signup")}
            </Button>
          </nav>

          {/* Mobile menu */}
          <div className="md:hidden">
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon">
                  <Menu className="h-6 w-6" />
                </Button>
              </SheetTrigger>
              <SheetContent className="bg-background border-border">
                <SheetHeader>
                  <SheetTitle className={isRTL ? "text-right" : "text-left"}>
                    {t("nav.menu")}
                  </SheetTitle>
                  <SheetDescription
                    className={isRTL ? "text-right" : "text-left"}
                  >
                    {t("nav.navigate")}
                  </SheetDescription>
                </SheetHeader>
                <nav className="flex flex-col space-y-4 mt-8">
                  <Link to="/">
                    <Button
                      variant="ghost"
                      className={`w-full ${
                        isRTL ? "justify-end" : "justify-start"
                      } text-foreground hover:text-primary`}
                    >
                      <Home className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                      {t("nav.home")}
                    </Button>
                  </Link>
                  <Button
                    variant="ghost"
                    className={`${
                      isRTL ? "justify-end" : "justify-start"
                    } text-foreground hover:text-primary`}
                  >
                    <Users className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                    {t("nav.about")}
                  </Button>
                  <Button
                    variant="ghost"
                    className={`${
                      isRTL ? "justify-end" : "justify-start"
                    } text-foreground hover:text-primary`}
                  >
                    <Building className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                    {t("nav.corporateStay")}
                  </Button>
                  <Button
                    variant="ghost"
                    className={`${
                      isRTL ? "justify-end" : "justify-start"
                    } text-foreground hover:text-primary`}
                  >
                    <MessageCircle className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                    {t("nav.contact")}
                  </Button>
                  <Button
                    variant="ghost"
                    className={`${
                      isRTL ? "justify-end" : "justify-start"
                    } text-foreground hover:text-primary`}
                  >
                    <Building2 className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                    {t("nav.realEstate")}
                  </Button>
                  <Button
                    variant="ghost"
                    className={`${
                      isRTL ? "justify-end" : "justify-start"
                    } text-foreground hover:text-primary`}
                  >
                    <LogIn className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                    {t("nav.login")}
                  </Button>
                  <Button className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-soft">
                    <UserPlus className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                    {t("nav.signup")}
                  </Button>
                </nav>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
