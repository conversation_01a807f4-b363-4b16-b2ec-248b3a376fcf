import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { HelpCircle, MessageCircle, ArrowRight } from "lucide-react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";

const FAQ = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const faqs = [
    {
      id: "1",
      question: t("faq.questions.howToBook.question"),
      answer: t("faq.questions.howToBook.answer")
    },
    {
      id: "2",
      question: t("faq.questions.whatIncluded.question"),
      answer: t("faq.questions.whatIncluded.answer")
    },
    {
      id: "3",
      question: t("faq.questions.cancellation.question"),
      answer: t("faq.questions.cancellation.answer")
    },
    {
      id: "4",
      question: t("faq.questions.access.question"),
      answer: t("faq.questions.access.answer")
    },
    {
      id: "5",
      question: t("faq.questions.help.question"),
      answer: t("faq.questions.help.answer")
    },
    {
      id: "6",
      question: t("faq.questions.pets.question"),
      answer: t("faq.questions.pets.answer")
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main>
        <section className="py-24 bg-background relative overflow-hidden">
          {/* Background decorations */}
          <div className="absolute top-20 right-10 opacity-5">
            <HelpCircle className="w-32 h-32 text-primary" />
          </div>
          <div className="absolute bottom-20 left-10 opacity-5">
            <MessageCircle className="w-24 h-24 text-primary" />
          </div>

          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <div className="inline-flex items-center bg-primary/10 rounded-full px-4 py-2 mb-6">
                <HelpCircle className="w-4 h-4 text-primary mr-2" />
                <span className="text-primary text-sm font-semibold">{t("faq.subtitle")}</span>
              </div>
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6 leading-tight">
                {t("faq.title")}
              </h1>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
                {t("faq.description")}
              </p>
            </div>

            <Card className="border-0 shadow-soft bg-gradient-to-br from-background to-brand-warm/20">
              <CardContent className="p-8">
                <Accordion type="single" collapsible className="w-full space-y-4">
                  {faqs.map((faq, index) => (
                    <AccordionItem 
                      key={faq.id} 
                      value={faq.id}
                      className="border border-border/50 rounded-xl px-6 bg-background/50 hover:bg-background/80 transition-colors duration-300"
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      <AccordionTrigger className="text-left font-semibold text-foreground hover:text-primary transition-colors duration-300">
                        {faq.question}
                      </AccordionTrigger>
                      <AccordionContent className="text-muted-foreground leading-relaxed pt-2">
                        {faq.answer}
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </CardContent>
            </Card>

            {/* Contact Support */}
            <div className="text-center mt-12">
              <div className="bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 rounded-2xl p-8">
                <MessageCircle className="w-12 h-12 text-primary mx-auto mb-4" />
                <h3 className="text-xl font-bold text-foreground mb-2">
                  {t("faq.stillHaveQuestions")}
                </h3>
                <p className="text-muted-foreground mb-6">
                  {t("faq.supportDescription")}
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button 
                    className="bg-primary hover:bg-primary/90 text-white shadow-soft hover:shadow-elevated transition-all duration-300"
                  >
                    <MessageCircle className="w-4 h-4 mr-2" />
                    {t("faq.liveChat")}
                  </Button>
                  <Button 
                    variant="outline"
                    className="border-primary text-primary hover:bg-primary hover:text-white transition-all duration-300"
                  >
                    {t("faq.contactSupport")}
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default FAQ;
