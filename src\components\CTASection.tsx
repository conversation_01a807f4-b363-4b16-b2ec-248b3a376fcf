import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowRight, Sparkles, Star, Users, MapPin } from "lucide-react";

const CTASection = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  return (
    <section className="py-24 bg-gradient-to-br from-primary/10 via-background to-brand-ocean/10 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary/5 to-transparent"></div>
      <div className="absolute top-20 left-20 opacity-10">
        <Sparkles className="w-32 h-32 text-primary animate-pulse" />
      </div>
      <div className="absolute bottom-20 right-20 opacity-10">
        <Star
          className="w-24 h-24 text-primary animate-pulse"
          style={{ animationDelay: "1s" }}
        />
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <Card className="border-0 shadow-elevated bg-gradient-to-br from-background via-background to-brand-warm/20 overflow-hidden">
          <CardContent className="p-12 md:p-16 text-center relative">
            {/* Decorative elements */}
            <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-primary via-brand-ocean to-primary"></div>
            <div className="absolute top-8 right-8 opacity-20">
              <MapPin className="w-16 h-16 text-primary" />
            </div>
            <div className="absolute bottom-8 left-8 opacity-20">
              <Users className="w-12 h-12 text-primary" />
            </div>

            {/* Content */}
            <div className="max-w-4xl mx-auto">
              <div className="inline-flex items-center bg-primary/10 rounded-full px-6 py-3 mb-8">
                <Sparkles
                  className={`w-5 h-5 text-primary ${isRTL ? "ml-2" : "mr-2"}`}
                />
                <span className="text-primary font-semibold">
                  {t("cta.badge")}
                </span>
              </div>

              <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-6 leading-tight">
                {t("cta.title")}
                <br />
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary via-brand-ocean to-primary">
                  {t("cta.subtitle")}
                </span>
              </h2>

              <p className="text-xl text-muted-foreground mb-12 leading-relaxed max-w-3xl mx-auto">
                {t("cta.description")}
              </p>

              {/* Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                <div className="text-center">
                  <div className="text-3xl md:text-4xl font-bold text-primary mb-2">
                    50K+
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {t("cta.stats.happyGuests")}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl md:text-4xl font-bold text-primary mb-2">
                    200+
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {t("cta.stats.premiumProperties")}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl md:text-4xl font-bold text-primary mb-2">
                    4.9★
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {t("cta.stats.averageRating")}
                  </div>
                </div>
              </div>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Button
                  size="lg"
                  className="bg-gradient-ocean hover:opacity-90 text-white shadow-soft hover:shadow-elevated transition-all duration-300 hover:scale-105 font-semibold px-8 py-6 text-lg"
                >
                  <Sparkles className={`w-5 h-5 ${isRTL ? "ml-2" : "mr-2"}`} />
                  {t("cta.buttons.exploreProperties")}
                  <ArrowRight
                    className={`w-5 h-5 ${isRTL ? "mr-2 rotate-180" : "ml-2"}`}
                  />
                </Button>

                <Button
                  variant="outline"
                  size="lg"
                  className="border-2 border-primary text-primary hover:bg-primary hover:text-white transition-all duration-300 font-semibold px-8 py-6 text-lg"
                >
                  {t("cta.buttons.learnMore")}
                </Button>
              </div>

              {/* Trust indicators */}
              <div className="mt-12 pt-8 border-t border-border/50">
                <div className="flex flex-wrap justify-center items-center gap-8 text-sm text-muted-foreground">
                  <div
                    className={`flex items-center ${
                      isRTL ? "space-x-reverse space-x-2" : "space-x-2"
                    }`}
                  >
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    <span>{t("cta.trustIndicators.trustedWorldwide")}</span>
                  </div>
                  <div
                    className={`flex items-center ${
                      isRTL ? "space-x-reverse space-x-2" : "space-x-2"
                    }`}
                  >
                    <Users className="w-4 h-4" />
                    <span>{t("cta.trustIndicators.customerSupport")}</span>
                  </div>
                  <div
                    className={`flex items-center ${
                      isRTL ? "space-x-reverse space-x-2" : "space-x-2"
                    }`}
                  >
                    <MapPin className="w-4 h-4" />
                    <span>{t("cta.trustIndicators.globalDestinations")}</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default CTASection;
