import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { useState, useEffect, useRef } from "react";
import { format } from "date-fns";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  ArrowLeft,
  ArrowRight,
  Star,
  Wifi,
  Car,
  Utensils,
  Tv,
  Wind,
  Bath,
  Bed,
  Users,
  MapPin,
  Calendar,
  Phone,
  Mail,
  MessageCircle,
  ChevronLeft,
  ChevronRight,
  Grid3X3,
  Heart,
  Share2,
  Camera,
  Shield,
  Award,
  Plus,
  Minus,
} from "lucide-react";
import { cn } from "@/lib/utils";

// Import images
import image1 from "@/assets/2e9a779a-39b0-402b-95e2-2ed025328301.jpg";
import image2 from "@/assets/7a373d21-f97a-49c7-a2f8-089bca45a13f.jpg";
import image3 from "@/assets/92106de0-dd6d-4b21-aff8-b438e2a6216a.jpg";
import image4 from "@/assets/647b6c1f-d7d3-4cb9-a3c3-dc433d23b357.jpg";
import image5 from "@/assets/92fcb9aa-effa-4243-a82d-4cd663f0b013.jpg";
import image6 from "@/assets/729cf90f-d9f7-47a9-b724-166c19eda1bc.jpg";
import image7 from "@/assets/302ded49-010c-4cfb-820a-e80248f358e8.jpg";
import image8 from "@/assets/9d8726ef-7525-46ab-9aa0-96b450e01622.jpg";

const PropertyDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showAllImages, setShowAllImages] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [checkIn, setCheckIn] = useState<Date>();
  const [checkOut, setCheckOut] = useState<Date>();
  const [guests, setGuests] = useState(1);

  // Mock property data - in a real app, this would come from an API
  const properties = {
    "1": {
      title: "شقة فاخرة في المركز",
      location: "شارع بيكر 98 | لندن",
      price: "200 جنيه إسترليني",
      rating: 4.8,
      bedrooms: 2,
      bathrooms: 1.5,
      guests: 4,
      images: [image1, image2, image3, image4, image5, image6, image7, image8],
    },
    "2": {
      title: "استوديو حديث",
      location: "شارع المركز 15 | دبي",
      price: "180 درهم إماراتي",
      rating: 4.6,
      bedrooms: 1,
      bathrooms: 1,
      guests: 2,
      images: [image2, image3, image4, image5, image6, image7, image8, image1],
    },
    "3": {
      title: "بنتهاوس مع إطلالة",
      location: "برج التجارة 45 | نيويورك",
      price: "350 دولار أمريكي",
      rating: 4.9,
      bedrooms: 3,
      bathrooms: 2,
      guests: 6,
      images: [image3, image4, image5, image6, image7, image8, image1, image2],
    },
    "4": {
      title: "فيلا عصرية",
      location: "حي الرياض الجديد | الرياض",
      price: "450 ريال سعودي",
      rating: 4.9,
      bedrooms: 4,
      bathrooms: 3,
      guests: 8,
      images: [image4, image5, image6, image7, image8, image1, image2, image3],
    },
    "5": {
      title: "شقة عائلية واسعة",
      location: "شارع الحمرا 22 | بيروت",
      price: "250 دولار أمريكي",
      rating: 4.7,
      bedrooms: 3,
      bathrooms: 2,
      guests: 6,
      images: [image5, image6, image7, image8, image1, image2, image3, image4],
    },
    "6": {
      title: "لوفت مع تراس",
      location: "حي السيف 88 | الكويت",
      price: "320 دينار كويتي",
      rating: 4.8,
      bedrooms: 2,
      bathrooms: 2,
      guests: 4,
      images: [image6, image7, image8, image1, image2, image3, image4, image5],
    },
  };

  const property = properties[id as keyof typeof properties];

  if (!property) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="container mx-auto px-4 py-16 text-center">
          <h1 className="text-2xl font-bold mb-4">العقار غير موجود</h1>
          <Button onClick={() => navigate("/")} variant="outline">
            العودة للصفحة الرئيسية
          </Button>
        </div>
        <Footer />
      </div>
    );
  }

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % property.images.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) =>
      prev === 0 ? property.images.length - 1 : prev - 1
    );
  };

  const amenities = [
    { icon: Wifi, name: "واي فاي" },
    { icon: Car, name: "موقف سيارات" },
    { icon: Utensils, name: "مطبخ مجهز" },
    { icon: Tv, name: "تلفزيون" },
    { icon: Wind, name: "مكيف هواء" },
    { icon: Bath, name: "حمام خاص" },
  ];

  // Helper functions - removed unused functions

  // Calculate number of nights and total price
  const calculateNights = () => {
    if (!checkIn || !checkOut) return 0;
    const diffTime = Math.abs(checkOut.getTime() - checkIn.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const nights = calculateNights();
  const basePrice = parseInt(property.price.replace(/[^0-9]/g, ""));
  const subtotal = nights * basePrice;
  const serviceFee = Math.round(subtotal * 0.08);
  const total = subtotal + serviceFee;

  return (
    <div className="min-h-screen bg-background">
      <Header />

      <main className="container mx-auto px-4 py-8">
        {/* Navigation and Actions */}
        <div className="flex items-center justify-between mb-8">
          <Button
            variant="ghost"
            onClick={() => navigate("/")}
            className="hover:bg-primary/10 transition-all duration-300 hover:scale-105"
          >
            {isRTL ? (
              <ArrowRight className="w-4 h-4 ml-2" />
            ) : (
              <ArrowLeft className="w-4 h-4 mr-2" />
            )}
            العودة
          </Button>

          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="sm"
              className="hover:bg-primary/10 transition-all duration-300"
            >
              <Share2 className="w-4 h-4 mr-2" />
              مشاركة
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsLiked(!isLiked)}
              className={`hover:bg-primary/10 transition-all duration-300 ${
                isLiked ? "text-red-500" : ""
              }`}
            >
              <Heart
                className={`w-4 h-4 mr-2 ${isLiked ? "fill-current" : ""}`}
              />
              حفظ
            </Button>
          </div>
        </div>

        {/* Property Title with Enhanced Design */}
        <div className="mb-8">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <h1 className="text-3xl md:text-4xl font-bold mb-3 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                {property.title}
              </h1>
              <div className="flex items-center flex-wrap gap-4 text-muted-foreground">
                <div className="flex items-center bg-yellow-50 dark:bg-yellow-900/20 px-3 py-1 rounded-full">
                  <Star className="w-4 h-4 fill-yellow-400 text-yellow-400 mr-1" />
                  <span className="font-semibold text-yellow-700 dark:text-yellow-400">
                    {property.rating}
                  </span>
                  <span className="text-xs mr-1">(124 تقييم)</span>
                </div>
                <div className="flex items-center">
                  <MapPin className="w-4 h-4 mr-1 text-primary" />
                  <span className="font-medium">{property.location}</span>
                </div>
                <Badge variant="secondary" className="flex items-center">
                  <Award className="w-3 h-3 mr-1" />
                  مضيف متميز
                </Badge>
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl md:text-3xl font-bold text-primary">
                {property.price}
              </div>
              <div className="text-sm text-muted-foreground">/ ليلة</div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Images and Details */}
          <div className="lg:col-span-2">
            {/* Enhanced Image Gallery */}
            <div className="relative mb-8 group">
              <div className="relative overflow-hidden rounded-xl shadow-2xl">
                <div className="aspect-[16/10] relative">
                  <img
                    src={property.images[currentImageIndex]}
                    alt={`${property.title} - صورة ${currentImageIndex + 1}`}
                    className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                  />

                  {/* Gradient Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>

                {/* Enhanced Image Navigation */}
                <button
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm text-gray-800 p-3 rounded-full hover:bg-white hover:scale-110 transition-all duration-300 opacity-0 group-hover:opacity-100 shadow-lg"
                >
                  <ChevronLeft className="w-5 h-5" />
                </button>
                <button
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm text-gray-800 p-3 rounded-full hover:bg-white hover:scale-110 transition-all duration-300 opacity-0 group-hover:opacity-100 shadow-lg"
                >
                  <ChevronRight className="w-5 h-5" />
                </button>

                {/* Enhanced Show All Images Button */}
                <button
                  onClick={() => setShowAllImages(true)}
                  className="absolute bottom-4 right-4 bg-white/90 backdrop-blur-sm text-gray-800 px-4 py-2 rounded-xl hover:bg-white transition-all duration-300 flex items-center shadow-lg hover:scale-105"
                >
                  <Camera className="w-4 h-4 mr-2" />
                  <span className="font-medium">
                    عرض جميع الصور ({property.images.length})
                  </span>
                </button>

                {/* Enhanced Image Counter */}
                <div className="absolute bottom-4 left-4 bg-black/60 backdrop-blur-sm text-white px-4 py-2 rounded-xl text-sm font-medium">
                  {currentImageIndex + 1} من {property.images.length}
                </div>

                {/* Image Dots Indicator */}
                <div className="absolute bottom-16 left-1/2 transform -translate-x-1/2 flex gap-2">
                  {property.images.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={`w-2 h-2 rounded-full transition-all duration-300 ${
                        currentImageIndex === index
                          ? "bg-white scale-125"
                          : "bg-white/50 hover:bg-white/75"
                      }`}
                    />
                  ))}
                </div>
              </div>

              {/* Enhanced Image Thumbnails */}
              <div className="grid grid-cols-6 md:grid-cols-8 gap-3 mt-6">
                {property.images.slice(0, 8).map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentImageIndex(index)}
                    className={`relative overflow-hidden rounded-lg aspect-square transition-all duration-300 hover:scale-105 ${
                      currentImageIndex === index
                        ? "ring-2 ring-primary ring-offset-2 shadow-lg"
                        : "hover:shadow-md"
                    }`}
                  >
                    <img
                      src={image}
                      alt={`صورة ${index + 1}`}
                      className={`w-full h-full object-cover transition-all duration-300 ${
                        currentImageIndex === index
                          ? "opacity-100"
                          : "opacity-70 hover:opacity-90"
                      }`}
                    />
                    {currentImageIndex === index && (
                      <div className="absolute inset-0 bg-primary/20 flex items-center justify-center">
                        <div className="w-3 h-3 bg-primary rounded-full shadow-lg"></div>
                      </div>
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* Enhanced Property Info Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="group relative bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-6 rounded-2xl border border-blue-200/50 dark:border-blue-700/50 hover:shadow-lg transition-all duration-300 hover:scale-105">
                <div className="flex items-center justify-center w-12 h-12 bg-blue-500 rounded-xl mb-4 mx-auto group-hover:rotate-12 transition-transform duration-300">
                  <Bed className="w-6 h-6 text-white" />
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-700 dark:text-blue-300 mb-1">
                    {property.bedrooms}
                  </div>
                  <div className="text-sm font-medium text-blue-600 dark:text-blue-400">
                    غرف نوم
                  </div>
                </div>
              </div>

              <div className="group relative bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/20 p-6 rounded-2xl border border-emerald-200/50 dark:border-emerald-700/50 hover:shadow-lg transition-all duration-300 hover:scale-105">
                <div className="flex items-center justify-center w-12 h-12 bg-emerald-500 rounded-xl mb-4 mx-auto group-hover:rotate-12 transition-transform duration-300">
                  <Bath className="w-6 h-6 text-white" />
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-emerald-700 dark:text-emerald-300 mb-1">
                    {property.bathrooms}
                  </div>
                  <div className="text-sm font-medium text-emerald-600 dark:text-emerald-400">
                    حمامات
                  </div>
                </div>
              </div>

              <div className="group relative bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-6 rounded-2xl border border-purple-200/50 dark:border-purple-700/50 hover:shadow-lg transition-all duration-300 hover:scale-105">
                <div className="flex items-center justify-center w-12 h-12 bg-purple-500 rounded-xl mb-4 mx-auto group-hover:rotate-12 transition-transform duration-300">
                  <Users className="w-6 h-6 text-white" />
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-700 dark:text-purple-300 mb-1">
                    {property.guests}
                  </div>
                  <div className="text-sm font-medium text-purple-600 dark:text-purple-400">
                    نزلاء
                  </div>
                </div>
              </div>
            </div>

            {/* Enhanced Overview */}
            <Card className="mb-8 border-0 shadow-lg bg-gradient-to-r from-background to-muted/20">
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  <div className="w-1 h-8 bg-gradient-to-b from-primary to-primary/50 rounded-full mr-4"></div>
                  <h2 className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                    نظرة عامة
                  </h2>
                </div>
                <p className="text-muted-foreground leading-relaxed text-lg">
                  يقع هذا المبنى في قلب{" "}
                  <span className="font-semibold text-primary">
                    {property.location}
                  </span>
                  ، ويوفر المزيج المثالي من حياة المدينة والهدوء. استمتع براحة
                  كونك على بُعد خطوات من المطاعم والتسوق والمعالم الثقافية
                  الشهيرة، مع الاستمتاع أيضًا بالهدوء والسكينة في حي سكني رئيسي.
                  تم تصميم هذه المساكن الواسعة بذكاء لتوفير مساحة معيشة وفيرة
                  تمكنك من الاستراحة والترفيه. سواء كنت ترغب في قضاء ليلة هادئة
                  في المنزل أو حفلة مع الأصدقاء، فإن منزلك هنا مجهز لتلبية
                  احتياجاتك.
                </p>

                {/* Trust Indicators */}
                <div className="flex items-center gap-6 mt-6 pt-6 border-t border-border/50">
                  <div className="flex items-center gap-2">
                    <Shield className="w-5 h-5 text-green-500" />
                    <span className="text-sm font-medium text-green-700 dark:text-green-400">
                      مكان آمن
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Award className="w-5 h-5 text-blue-500" />
                    <span className="text-sm font-medium text-blue-700 dark:text-blue-400">
                      مضيف متميز
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Enhanced Amenities */}
            <Card className="mb-8 border-0 shadow-lg bg-gradient-to-r from-background to-muted/20">
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  <div className="w-1 h-8 bg-gradient-to-b from-primary to-primary/50 rounded-full mr-4"></div>
                  <h2 className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                    المرافق والخدمات
                  </h2>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {amenities.map((amenity, index) => (
                    <div
                      key={index}
                      className="group flex items-center p-4 bg-gradient-to-r from-muted/30 to-muted/10 rounded-xl hover:from-primary/10 hover:to-primary/5 transition-all duration-300 hover:scale-105 hover:shadow-md border border-border/50"
                    >
                      <div className="flex items-center justify-center w-10 h-10 bg-primary/10 rounded-lg mr-3 group-hover:bg-primary/20 transition-colors duration-300">
                        <amenity.icon className="w-5 h-5 text-primary group-hover:scale-110 transition-transform duration-300" />
                      </div>
                      <span className="font-medium text-foreground group-hover:text-primary transition-colors duration-300">
                        {amenity.name}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Enhanced Rules */}
            <Card className="border-0 shadow-lg bg-gradient-to-r from-background to-muted/20">
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  <div className="w-1 h-8 bg-gradient-to-b from-primary to-primary/50 rounded-full mr-4"></div>
                  <h2 className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                    قواعد مكان الإقامة
                  </h2>
                </div>
                <div className="space-y-6">
                  <div className="group p-6 bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-xl border border-orange-200/50 dark:border-orange-700/50 hover:shadow-md transition-all duration-300">
                    <div className="flex items-start">
                      <div className="flex items-center justify-center w-10 h-10 bg-orange-500 rounded-lg mr-4 mt-1">
                        <Shield className="w-5 h-5 text-white" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-bold text-orange-800 dark:text-orange-300 mb-2">
                          مبلغ التأمين
                        </h3>
                        <p className="text-orange-700 dark:text-orange-400">
                          نطلب إيداع مبلغ تأمين بقيمة{" "}
                          <span className="font-bold">1350 جنيه إسترليني</span>{" "}
                          إلزامي قبل تسجيل الوصول.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="group p-6 bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 rounded-xl border border-red-200/50 dark:border-red-700/50 hover:shadow-md transition-all duration-300">
                    <div className="flex items-start">
                      <div className="flex items-center justify-center w-10 h-10 bg-red-500 rounded-lg mr-4 mt-1">
                        <Wind className="w-5 h-5 text-white" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-bold text-red-800 dark:text-red-300 mb-2">
                          التدخين
                        </h3>
                        <p className="text-red-700 dark:text-red-400">
                          التدخين ممنوع. يؤدي مخالفة هذه القاعدة إلى دفع غرامة
                          قدرها
                          <span className="font-bold"> 500 جنيه استرليني</span>.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="group p-6 bg-gradient-to-r from-amber-50 to-amber-100 dark:from-amber-900/20 dark:to-amber-800/20 rounded-xl border border-amber-200/50 dark:border-amber-700/50 hover:shadow-md transition-all duration-300">
                    <div className="flex items-start">
                      <div className="flex items-center justify-center w-10 h-10 bg-amber-500 rounded-lg mr-4 mt-1">
                        <Heart className="w-5 h-5 text-white" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-bold text-amber-800 dark:text-amber-300 mb-2">
                          سياسة الحيوانات الأليفة
                        </h3>
                        <p className="text-amber-700 dark:text-amber-400">
                          الحيوانات الأليفة غير مسموح بها. يترتب على أي انتهاك
                          لهذه السياسة غرامة قدرها{" "}
                          <span className="font-bold">500 جنيه استرليني</span>.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Enhanced Right Column - Booking */}
          <div className="lg:col-span-1">
            <Card className="sticky top-8 border-0 shadow-2xl bg-gradient-to-br from-background via-background to-muted/10">
              <CardContent className="p-8">
                {/* Price Section with Enhanced Design */}
                <div className="text-center mb-8 p-6 bg-gradient-to-r from-primary/5 to-primary/10 rounded-2xl border border-primary/20">
                  <div className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent mb-3">
                    من {property.price}
                  </div>
                  <div className="text-sm text-muted-foreground mb-4">
                    / ليلة
                  </div>
                  <div className="flex items-center justify-center bg-yellow-50 dark:bg-yellow-900/20 px-4 py-2 rounded-full">
                    <Star className="w-5 h-5 fill-yellow-400 text-yellow-400 mr-2" />
                    <span className="font-bold text-yellow-700 dark:text-yellow-400">
                      {property.rating}
                    </span>
                    <span className="text-xs text-yellow-600 dark:text-yellow-500 mr-1">
                      (124 تقييم)
                    </span>
                  </div>
                </div>

                {/* Enhanced Booking Form */}
                <div className="space-y-6 mb-8">
                  <div>
                    <label className="block text-sm font-bold mb-3 text-foreground">
                      اختر التواريخ
                    </label>
                    <div className="grid grid-cols-2 gap-3">
                      {/* Check-in Date */}
                      <div className="relative">
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full justify-start h-12 bg-gradient-to-r from-background to-muted/20 border-2 hover:border-primary/50 transition-all duration-300",
                                !checkIn && "text-muted-foreground",
                                checkIn && "border-primary/30"
                              )}
                            >
                              <Calendar className="w-4 h-4 mr-2 text-primary" />
                              <div className="text-right">
                                <div className="text-xs text-muted-foreground">
                                  وصول
                                </div>
                                <div className="text-sm font-medium">
                                  {checkIn
                                    ? format(checkIn, "MMM dd")
                                    : "اختر تاريخ"}
                                </div>
                              </div>
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent
                            className="w-auto p-0 bg-background border border-border shadow-xl rounded-xl z-50"
                            align="start"
                          >
                            <CalendarComponent
                              mode="single"
                              selected={checkIn}
                              onSelect={(date) => {
                                setCheckIn(date);
                                // Auto-set checkout to next day if not already set
                                if (!checkOut && date) {
                                  const nextDay = new Date(date);
                                  nextDay.setDate(nextDay.getDate() + 1);
                                  setCheckOut(nextDay);
                                }
                              }}
                              disabled={(date) => date < new Date()}
                              initialFocus
                              className="rounded-xl"
                            />
                          </PopoverContent>
                        </Popover>
                      </div>

                      {/* Check-out Date */}
                      <div className="relative">
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full justify-start h-12 bg-gradient-to-r from-background to-muted/20 border-2 hover:border-primary/50 transition-all duration-300",
                                !checkOut && "text-muted-foreground",
                                checkOut && "border-primary/30"
                              )}
                            >
                              <Calendar className="w-4 h-4 mr-2 text-primary" />
                              <div className="text-right">
                                <div className="text-xs text-muted-foreground">
                                  مغادرة
                                </div>
                                <div className="text-sm font-medium">
                                  {checkOut
                                    ? format(checkOut, "MMM dd")
                                    : "اختر تاريخ"}
                                </div>
                              </div>
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent
                            className="w-auto p-0 bg-background border border-border shadow-xl rounded-xl z-50"
                            align="start"
                          >
                            <CalendarComponent
                              mode="single"
                              selected={checkOut}
                              onSelect={setCheckOut}
                              disabled={(date) =>
                                date < new Date() ||
                                (checkIn && date <= checkIn)
                              }
                              initialFocus
                              className="rounded-xl"
                            />
                          </PopoverContent>
                        </Popover>
                      </div>
                    </div>
                  </div>

                  {/* Guest Selection */}
                  <div className="relative">
                    <label className="block text-sm font-bold mb-3 text-foreground">
                      عدد النزلاء
                    </label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-between h-12 bg-gradient-to-r from-background to-muted/20 border-2 hover:border-primary/50 transition-all duration-300"
                        >
                          <div className="flex items-center">
                            <Users className="w-4 h-4 mr-2 text-primary" />
                            <span className="font-medium">
                              {guests} {guests === 1 ? "نزيل" : "نزلاء"}
                            </span>
                          </div>
                          <ChevronRight className="w-4 h-4 text-muted-foreground transition-transform duration-300" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-72 p-6 bg-background border border-border shadow-xl rounded-xl z-50"
                        align="start"
                      >
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <div className="font-medium text-foreground">
                                البالغون
                              </div>
                              <div className="text-sm text-muted-foreground">
                                13 سنة أو أكثر
                              </div>
                            </div>
                            <div className="flex items-center gap-3">
                              <Button
                                variant="outline"
                                size="icon"
                                className="h-10 w-10 rounded-full border-border hover:bg-primary/10 hover:border-primary/30"
                                onClick={() =>
                                  setGuests(Math.max(1, guests - 1))
                                }
                                disabled={guests <= 1}
                              >
                                <Minus className="h-4 w-4" />
                              </Button>
                              <span className="w-12 text-center text-lg font-semibold text-foreground">
                                {guests}
                              </span>
                              <Button
                                variant="outline"
                                size="icon"
                                className="h-10 w-10 rounded-full border-border hover:bg-primary/10 hover:border-primary/30"
                                onClick={() =>
                                  setGuests(
                                    Math.min(property.guests, guests + 1)
                                  )
                                }
                                disabled={guests >= property.guests}
                              >
                                <Plus className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>

                          <div className="text-xs text-muted-foreground pt-2 border-t border-border">
                            الحد الأقصى: {property.guests} نزلاء
                          </div>
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                {/* Enhanced CTA Button */}
                <Button
                  disabled={!checkIn || !checkOut || nights === 0}
                  className={`w-full mb-6 h-12 text-lg font-bold transition-all duration-300 shadow-lg ${
                    checkIn && checkOut && nights > 0
                      ? "bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 hover:shadow-xl hover:scale-105"
                      : "bg-muted cursor-not-allowed"
                  }`}
                >
                  {!checkIn || !checkOut ? "اختر التواريخ أولاً" : "احجز الآن"}
                </Button>

                {/* Dynamic Pricing Breakdown */}
                {nights > 0 && (
                  <div className="bg-muted/20 rounded-xl p-4 mb-6">
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>
                          {property.price} × {nights}{" "}
                          {nights === 1 ? "ليلة" : "ليالي"}
                        </span>
                        <span>
                          {subtotal.toLocaleString()}{" "}
                          {property.price.split(" ").slice(-1)[0]}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>رسوم الخدمة</span>
                        <span>
                          {serviceFee.toLocaleString()}{" "}
                          {property.price.split(" ").slice(-1)[0]}
                        </span>
                      </div>
                      <div className="border-t pt-2 mt-2">
                        <div className="flex justify-between font-bold">
                          <span>المجموع</span>
                          <span>
                            {total.toLocaleString()}{" "}
                            {property.price.split(" ").slice(-1)[0]}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <p className="text-center text-sm text-muted-foreground mb-8 bg-green-50 dark:bg-green-900/20 p-3 rounded-lg border border-green-200/50 dark:border-green-700/50">
                  ✓ لن تُحاسب بعد - حجز مجاني
                </p>

                {/* Enhanced Contact Info */}
                <div className="border-t border-border/50 pt-6 space-y-4">
                  <h3 className="font-bold text-lg bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                    تواصل معنا
                  </h3>
                  <div className="space-y-3">
                    <a
                      href="tel:+000004000"
                      className="flex items-center p-3 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg hover:shadow-md transition-all duration-300 text-blue-700 dark:text-blue-400 hover:scale-105"
                    >
                      <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                        <Phone className="w-4 h-4 text-white" />
                      </div>
                      <span className="font-medium">+0200000</span>
                    </a>
                    <a
                      href="mailto:<EMAIL>"
                      className="flex items-center p-3 bg-gradient-to-r from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/20 rounded-lg hover:shadow-md transition-all duration-300 text-emerald-700 dark:text-emerald-400 hover:scale-105"
                    >
                      <div className="w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center mr-3">
                        <Mail className="w-4 h-4 text-white" />
                      </div>
                      <span className="font-medium"><EMAIL></span>
                    </a>
                    <Button
                      variant="outline"
                      className="w-full h-12 bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border-green-200 dark:border-green-700 text-green-700 dark:text-green-400 hover:bg-green-100 dark:hover:bg-green-800/30 transition-all duration-300 hover:scale-105"
                    >
                      <MessageCircle className="w-4 h-4 mr-2" />
                      تواصل عبر واتساب
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>

      {/* Enhanced Image Gallery Modal */}
      {showAllImages && (
        <div className="fixed inset-0 bg-black/95 backdrop-blur-sm z-50 flex items-center justify-center p-4 animate-in fade-in duration-300">
          <div className="max-w-7xl w-full max-h-full overflow-auto">
            <div className="flex justify-between items-center mb-8 bg-black/50 backdrop-blur-sm p-4 rounded-2xl">
              <div>
                <h2 className="text-white text-2xl font-bold mb-1">
                  معرض الصور
                </h2>
                <p className="text-white/70 text-sm">
                  {property.images.length} صورة
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAllImages(false)}
                className="text-white hover:bg-white/20 h-12 w-12 rounded-full"
              >
                ✕
              </Button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {property.images.map((image, index) => (
                <div
                  key={index}
                  className="group relative overflow-hidden rounded-2xl cursor-pointer hover:scale-105 transition-all duration-300"
                  onClick={() => {
                    setCurrentImageIndex(index);
                    setShowAllImages(false);
                  }}
                >
                  <img
                    src={image}
                    alt={`${property.title} - صورة ${index + 1}`}
                    className="w-full h-64 object-cover group-hover:brightness-110 transition-all duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="absolute bottom-4 left-4 right-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="font-medium">صورة {index + 1}</div>
                  </div>
                  <div className="absolute top-4 right-4 bg-black/50 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    عرض
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      <Footer />
    </div>
  );
};

export default PropertyDetails;
