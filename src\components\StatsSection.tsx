import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { TrendingUp, Users, Heart, Award } from "lucide-react";

const getStats = (t: any) => [
  {
    value: 50,
    suffix: "K+",
    labelKey: "stats.happyGuests",
    icon: Users,
    color: "from-blue-500 to-blue-600",
    description: "Satisfied customers worldwide",
  },
  {
    value: 200,
    suffix: "+",
    labelKey: "stats.premiumProperties",
    icon: TrendingUp,
    color: "from-green-500 to-green-600",
    description: "Premium accommodations",
  },
  {
    value: 99,
    suffix: "%",
    labelKey: "stats.satisfactionRate",
    icon: Heart,
    color: "from-pink-500 to-pink-600",
    description: "Customer satisfaction rate",
  },
];

const AnimatedCounter = ({
  value,
  suffix,
}: {
  value: number;
  suffix: string;
}) => {
  const [count, setCount] = useState(0);

  useEffect(() => {
    const duration = 2000; // 2 seconds
    const steps = 60;
    const stepValue = value / steps;
    const stepTime = duration / steps;

    let currentStep = 0;
    const timer = setInterval(() => {
      currentStep++;
      setCount(Math.min(Math.round(stepValue * currentStep), value));

      if (currentStep >= steps) {
        clearInterval(timer);
      }
    }, stepTime);

    return () => clearInterval(timer);
  }, [value]);

  return (
    <span className="text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-primary to-brand-ocean bg-clip-text text-transparent">
      {count}
      {suffix}
    </span>
  );
};

const StatsSection = () => {
  const { t } = useTranslation();
  const stats = getStats(t);

  return (
    <section className="py-24 bg-gradient-to-br from-background via-brand-warm/20 to-background relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute top-0 left-0 w-full h-full opacity-5">
        <div className="absolute top-20 left-10">
          <Award className="w-24 h-24 text-primary" />
        </div>
        <div className="absolute bottom-20 right-10">
          <TrendingUp className="w-20 h-20 text-primary" />
        </div>
      </div>
    </section>
  );
};

export default StatsSection;
