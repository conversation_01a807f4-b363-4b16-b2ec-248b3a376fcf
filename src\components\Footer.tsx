import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Instagram, Twitter, Linkedin, Facebook } from "lucide-react";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import { Link } from "react-router-dom";

const Footer = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  return (
    <footer className="bg-foreground text-background py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <div
              className={`flex items-center mb-6 ${
                isRTL ? "space-x-reverse space-x-2" : "space-x-2"
              }`}
            >
              <div className="w-8 h-8 bg-gradient-ocean rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">H</span>
              </div>
              <span className="text-xl font-semibold">hala</span>
            </div>
            <p className="text-background/80 leading-relaxed mb-6">
              {t("footer.description")}
            </p>
            <div
              className={`flex ${
                isRTL ? "space-x-reverse space-x-4" : "space-x-4"
              }`}
            >
              <Button
                variant="ghost"
                size="icon"
                className="text-background/80 hover:text-background hover:bg-background/10"
              >
                <Instagram className="h-5 w-5" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="text-background/80 hover:text-background hover:bg-background/10"
              >
                <Twitter className="h-5 w-5" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="text-background/80 hover:text-background hover:bg-background/10"
              >
                <Linkedin className="h-5 w-5" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="text-background/80 hover:text-background hover:bg-background/10"
              >
                <Facebook className="h-5 w-5" />
              </Button>
            </div>
          </div>

          {/* Company */}
          <div>
            <h3 className="text-lg font-semibold mb-6">
              {t("footer.company")}
            </h3>
            <ul className="space-y-4">
              <li>
                <a
                  href="#"
                  className="text-background/80 hover:text-background transition-colors"
                >
                  {t("footer.aboutUs")}
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-background/80 hover:text-background transition-colors"
                >
                  {t("footer.careers")}
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-background/80 hover:text-background transition-colors"
                >
                  {t("footer.press")}
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-background/80 hover:text-background transition-colors"
                >
                  {t("footer.blog")}
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-background/80 hover:text-background transition-colors"
                >
                  {t("footer.investorRelations")}
                </a>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="text-lg font-semibold mb-6">
              {t("footer.support")}
            </h3>
            <ul className="space-y-4">
              <li>
                <Link
                  to="/faq"
                  className="text-background/80 hover:text-background transition-colors"
                >
                  {t("faq.title")}
                </Link>
              </li>
              <li>
                <a
                  href="#"
                  className="text-background/80 hover:text-background transition-colors"
                >
                  {t("footer.helpCenter")}
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-background/80 hover:text-background transition-colors"
                >
                  {t("footer.contactUs")}
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-background/80 hover:text-background transition-colors"
                >
                  {t("footer.safetySecurity")}
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-background/80 hover:text-background transition-colors"
                >
                  {t("footer.communityGuidelines")}
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-background/80 hover:text-background transition-colors"
                >
                  {t("footer.accessibility")}
                </a>
              </li>
            </ul>
          </div>

          {/* Newsletter */}
          <div>
            <h3 className="text-lg font-semibold mb-6">
              {t("footer.stayUpdated")}
            </h3>
            <p className="text-background/80 mb-4">
              {t("footer.newsletterDescription")}
            </p>
            <div className="space-y-3">
              <Input
                type="email"
                placeholder={t("footer.emailPlaceholder")}
                className="bg-background/10 border-background/20 text-background placeholder:text-background/60 focus:bg-background/20"
              />
              <Button className="w-full bg-brand-teal hover:bg-brand-teal-dark text-white">
                {t("footer.subscribe")}
              </Button>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="pt-8 border-t border-background/20">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-background/60 text-sm">
              {t("footer.copyright")}
            </p>
            <div
              className={`flex text-sm ${
                isRTL ? "space-x-reverse space-x-6" : "space-x-6"
              }`}
            >
              <a
                href="#"
                className="text-background/60 hover:text-background transition-colors"
              >
                {t("footer.privacyPolicy")}
              </a>
              <a
                href="#"
                className="text-background/60 hover:text-background transition-colors"
              >
                {t("footer.termsOfService")}
              </a>
              <a
                href="#"
                className="text-background/60 hover:text-background transition-colors"
              >
                {t("footer.cookiePolicy")}
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
